Format: https://www.debian.org/doc/packaging-manuals/copyright-format/1.0/
Upstream-Name: dde-application-manager
Upstream-Contact: UnionTech Software Technology Co., Ltd. <>
Source: https://github.com/linuxdeepin/dde-application-manager

# README & DOC
Files: README.md README.zh_CN.md docs/* examples/launchApp/README.md
Copyright: UnionTech Software Technology Co., Ltd.
License: CC-BY-4.0

# Project file
Files: *CMakeLists.txt
Copyright: None
License: CC0-1.0

# ci
Files: .github/* .gitlab-ci.yml .obs/workflows.yml
Copyright: None
License: CC0-1.0

# gitignore
Files: .gitignore
Copyright: None
License: CC0-1.0

# cmake
Files: api/*.cmake.in
Copyright: None
License: CC0-1.0

# DBus API
Files: api/dbus/*.xml apps/app-update-notifier/api/dbus/*.xml
Copyright: None
License: CC0-1.0

# Packaging configuration
Files: rpm/* debian/* archlinux/*
Copyright: None
License: CC0-1.0

# test
Files: tests/data/*
Copyright: None
License: CC0-1.0

# hook
Files: misc/dpkg/dpkg.cfg.d/* misc/hooks.d/*
Copyright: None
License: CC0-1.0

# configure file
Files: misc/dsg/*
Copyright: None
License: CC0-1.0

Files: toolGenerate/**/*
Copyright: None
License: CC0-1.0


