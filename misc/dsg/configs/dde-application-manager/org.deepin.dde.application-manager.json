{"magic": "dsg.config.meta", "version": "1.0", "contents": {"appExtraEnvironments": {"value": [], "serial": 0, "flags": [], "name": "Launching app with extra environments", "name[zh_CN]": "启动应用时附加额外环境变量", "description": "Launching app with extra environments", "permissions": "readwrite", "visibility": "public"}, "appEnvironmentsBlacklist": {"value": [], "serial": 0, "flags": [], "name": "Ignore blacklisted environment variables before launching app", "name[zh_CN]": "启动应用时取消某些环境变量", "description": "Ignore blacklisted environment variables before launching app", "permissions": "readwrite", "visibility": "public"}}}