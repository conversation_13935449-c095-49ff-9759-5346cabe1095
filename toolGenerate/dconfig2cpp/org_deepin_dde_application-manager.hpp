/**
 * This file is generated by dconfig2cpp.
 * Command line arguments: ./dconfig2cpp -p ./dde-application-manager/toolGenerate/dconfig2cpp ./dde-application-manager/misc/dsg/configs/dde-application-manager/org.deepin.dde.application-manager.json
 * Generation time: 2025-01-14T10:54:59
 * JSON file version: 1.0
 * 
 * WARNING: DO NOT MODIFY THIS FILE MANUALLY.
 * If you need to change the content, please modify the dconfig2cpp tool.
 */

#ifndef ORG_DEEPIN_DDE_APPLICATION-MANAGER_H
#define ORG_DEEPIN_DDE_APPLICATION-MANAGER_H

#include <QThread>
#include <QVariant>
#include <QDebug>
#include <QAtomicPointer>
#include <QAtomicInteger>
#include <DConfig>

class org_deepin_dde_application-manager : public QObject {
    Q_OBJECT

    Q_PROPERTY(QList<QVariant> appEnvironmentsBlacklist READ appEnvironmentsBlacklist WRITE setAppEnvironmentsBlacklist NOTIFY appEnvironmentsBlacklistChanged)
    Q_PROPERTY(QList<QVariant> appExtraEnvironments READ appExtraEnvironments WRITE setAppExtraEnvironments NOTIFY appExtraEnvironmentsChanged)
public:
    explicit org_deepin_dde_application-manager(QThread *thread, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_application-manager(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &appId, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, appId, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_application-manager(QThread *thread, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    explicit org_deepin_dde_application-manager(QThread *thread, DTK_CORE_NAMESPACE::DConfigBackend *backend, const QString &name, const QString &subpath, QObject *parent = nullptr)
        : QObject(parent) {

        if (!thread->isRunning()) {
            qWarning() << QStringLiteral("Warning: The provided thread is not running.");
        }
        Q_ASSERT(QThread::currentThread() != thread);
        auto worker = new QObject();
        worker->moveToThread(thread);
        QMetaObject::invokeMethod(worker, [=]() {
            auto config = DTK_CORE_NAMESPACE::DConfig::create(backend, name, subpath, nullptr);
            if (!config) {
                qWarning() << QStringLiteral("Failed to create DConfig instance.");
                worker->deleteLater();
                return;
            }
            config->moveToThread(QThread::currentThread());
            initialize(config);
            worker->deleteLater();
        });
    }
    ~org_deepin_dde_application-manager() {
        if (m_config.loadRelaxed()) {
            m_config.loadRelaxed()->deleteLater();
        }
    }

    QList<QVariant> appEnvironmentsBlacklist() const {
        return p_appEnvironmentsBlacklist;
    }
    void setAppEnvironmentsBlacklist(const QList<QVariant> &value) {
        auto oldValue = p_appEnvironmentsBlacklist;
        p_appEnvironmentsBlacklist = value;
        markPropertySet(0);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("appEnvironmentsBlacklist"), value);
            });
        }
        if (p_appEnvironmentsBlacklist != oldValue) {
            Q_EMIT appEnvironmentsBlacklistChanged();
        }
    }
    QList<QVariant> appExtraEnvironments() const {
        return p_appExtraEnvironments;
    }
    void setAppExtraEnvironments(const QList<QVariant> &value) {
        auto oldValue = p_appExtraEnvironments;
        p_appExtraEnvironments = value;
        markPropertySet(1);
        if (auto config = m_config.loadRelaxed()) {
            QMetaObject::invokeMethod(config, [this, value]() {
                m_config.loadRelaxed()->setValue(QStringLiteral("appExtraEnvironments"), value);
            });
        }
        if (p_appExtraEnvironments != oldValue) {
            Q_EMIT appExtraEnvironmentsChanged();
        }
    }
Q_SIGNALS:
    void appEnvironmentsBlacklistChanged();
    void appExtraEnvironmentsChanged();
private:
    void initialize(DTK_CORE_NAMESPACE::DConfig *config) {
        Q_ASSERT(!m_config.loadRelaxed());
        m_config.storeRelaxed(config);
        if (testPropertySet(0)) {
            config->setValue(QStringLiteral("appEnvironmentsBlacklist"), QVariant::fromValue(p_appEnvironmentsBlacklist));
        } else {
            updateValue(QStringLiteral("appEnvironmentsBlacklist"), QVariant::fromValue(p_appEnvironmentsBlacklist));
        }
        if (testPropertySet(1)) {
            config->setValue(QStringLiteral("appExtraEnvironments"), QVariant::fromValue(p_appExtraEnvironments));
        } else {
            updateValue(QStringLiteral("appExtraEnvironments"), QVariant::fromValue(p_appExtraEnvironments));
        }

        connect(config, &DTK_CORE_NAMESPACE::DConfig::valueChanged, this, [this](const QString &key) {
            updateValue(key);
        }, Qt::DirectConnection);
    }
    void updateValue(const QString &key, const QVariant &fallback = QVariant()) {
        Q_ASSERT(QThread::currentThread() == m_config.loadRelaxed()->thread());
        const QVariant &value = m_config.loadRelaxed()->value(key, fallback);
        if (key == QStringLiteral("appEnvironmentsBlacklist")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_appEnvironmentsBlacklist != newValue) {
                    p_appEnvironmentsBlacklist = newValue;
                    Q_EMIT appEnvironmentsBlacklistChanged();
                }
            });
            return;
        }
        if (key == QStringLiteral("appExtraEnvironments")) {
            auto newValue = qvariant_cast<QList<QVariant>>(value);
            QMetaObject::invokeMethod(this, [this, newValue]() {
                if (p_appExtraEnvironments != newValue) {
                    p_appExtraEnvironments = newValue;
                    Q_EMIT appExtraEnvironmentsChanged();
                }
            });
            return;
        }
    }
    inline void markPropertySet(const int index) {
        if (index < 32) {
            m_propertySetStatus0.fetchAndOrOrdered(1 << (index - 0));
            return;
        }
        Q_UNREACHABLE();
    }
    inline bool testPropertySet(const int index) const {
        if (index < 32) {
            return (m_propertySetStatus0.loadRelaxed() & (1 << (index - 0)));
        }
        Q_UNREACHABLE();
    }
    QAtomicPointer<DTK_CORE_NAMESPACE::DConfig> m_config = nullptr;
    QList<QVariant> p_appEnvironmentsBlacklist { QList<QVariant>{} };
    QList<QVariant> p_appExtraEnvironments { QList<QVariant>{} };
    QAtomicInteger<quint32> m_propertySetStatus0 = 0;
};

#endif // ORG_DEEPIN_DDE_APPLICATION-MANAGER_H
