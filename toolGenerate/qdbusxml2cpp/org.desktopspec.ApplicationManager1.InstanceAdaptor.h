/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-application-manager/api/dbus/org.desktopspec.ApplicationManager1.Instance.xml -a ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.InstanceAdaptor -i ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.Instance.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DESKTOPSPEC_APPLICATIONMANAGER1_INSTANCEADAPTOR_H
#define ORG_DESKTOPSPEC_APPLICATIONMANAGER1_INSTANCEADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.Instance.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.desktopspec.ApplicationManager1.Instance
 */
class InstanceAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.desktopspec.ApplicationManager1.Instance")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.desktopspec.ApplicationManager1.Instance\">\n"
"    <property access=\"read\" type=\"o\" name=\"Application\">\n"
"      <annotation value=\"Object path of the Application.                         That DBus object will impelement                        org.desktopspec.ApplicationManager1.Application.                        NOTE:                        If the application is uninstalled, this object path                        will be set to `/`.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"o\" name=\"SystemdUnitPath\">\n"
"      <annotation value=\"The systemd unit object path of this instance                        under systemd user daemon.                        Some other DE compoments can use this path to                        use the cgroup interface provided by systemd.                        NOTE:                        This property MIGHT be empty                        if this instance is not managed by systemd.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"s\" name=\"Launcher\">\n"
"      <annotation value=\"This property indicates which Application                        launcher started this instance.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <property access=\"read\" type=\"b\" name=\"Orphaned\">\n"
"      <annotation value=\"This property indicates that the application                         to which the instance belonged has been removed.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <method name=\"KillAll\">\n"
"      <arg direction=\"in\" type=\"i\" name=\"signal\"/>\n"
"      <annotation value=\"Force kill this instance.                                 ATTENTION: All processes which launched by                                  this instance will be killed.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </method>\n"
"  </interface>\n"
        "")
public:
    InstanceAdaptor(QObject *parent);
    virtual ~InstanceAdaptor();

public: // PROPERTIES
    Q_PROPERTY(QDBusObjectPath Application READ application)
    QDBusObjectPath application() const;

    Q_PROPERTY(QString Launcher READ launcher)
    QString launcher() const;

    Q_PROPERTY(bool Orphaned READ orphaned)
    bool orphaned() const;

    Q_PROPERTY(QDBusObjectPath SystemdUnitPath READ systemdUnitPath)
    QDBusObjectPath systemdUnitPath() const;

public Q_SLOTS: // METHODS
    void KillAll(int signal);
Q_SIGNALS: // SIGNALS
};

#endif
