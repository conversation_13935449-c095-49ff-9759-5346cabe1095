/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-application-manager/api/dbus/org.desktopspec.MimeManager1.xml -a ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.MimeManager1Adaptor -i ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.MimeManager1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DESKTOPSPEC_MIMEMANAGER1ADAPTOR_H
#define ORG_DESKTOPSPEC_MIMEMANAGER1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.MimeManager1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.desktopspec.MimeManager1
 */
class MimeManager1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.desktopspec.MimeManager1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.desktopspec.MimeManager1\">\n"
"    <method name=\"queryDefaultApplication\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"content\"/>\n"
"      <arg direction=\"out\" type=\"s\" name=\"mimeType\"/>\n"
"      <arg direction=\"out\" type=\"o\" name=\"application\"/>\n"
"      <annotation value=\"content can be absolute path of a file or a mime type.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </method>\n"
"    <method name=\"setDefaultApplication\">\n"
"      <arg direction=\"in\" type=\"a{ss}\" name=\"defaultApps\"/>\n"
"      <annotation value=\"QStringMap\" name=\"org.qtproject.QtDBus.QtTypeName.In0\"/>\n"
"    </method>\n"
"    <method name=\"unsetDefaultApplication\">\n"
"      <arg direction=\"in\" type=\"as\" name=\"mimeTypes\"/>\n"
"    </method>\n"
"    <method name=\"listApplications\">\n"
"      <arg direction=\"in\" type=\"s\" name=\"mimeType\"/>\n"
"      <arg direction=\"out\" type=\"a{oa{sa{sv}}}\" name=\"applications_and_properties\"/>\n"
"      <annotation value=\"ObjectMap\" name=\"org.qtproject.QtDBus.QtTypeName.Out0\"/>\n"
"    </method>\n"
"  </interface>\n"
        "")
public:
    MimeManager1Adaptor(QObject *parent);
    virtual ~MimeManager1Adaptor();

public: // PROPERTIES
public Q_SLOTS: // METHODS
    ObjectMap listApplications(const QString &mimeType);
    QString queryDefaultApplication(const QString &content, QDBusObjectPath &application);
    void setDefaultApplication(const QStringMap &defaultApps);
    void unsetDefaultApplication(const QStringList &mimeTypes);
Q_SIGNALS: // SIGNALS
};

#endif
