/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-application-manager/apps/app-update-notifier/api/dbus/org.desktopspec.ApplicationUpdateNotifier1.xml -a ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationUpdateNotifier1Adaptor -i ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationUpdateNotifier1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DESKTOPSPEC_APPLICATIONUPDATENOTIFIER1ADAPTOR_H
#define ORG_DESKTOPSPEC_APPLICATIONUPDATENOTIFIER1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationUpdateNotifier1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.desktopspec.ApplicationUpdateNotifier1
 */
class ApplicationUpdateNotifier1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.desktopspec.ApplicationUpdateNotifier1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.desktopspec.ApplicationUpdateNotifier1\">\n"
"    <signal name=\"ApplicationUpdated\">\n"
"      <annotation value=\"This signal will emit when application's                        infomations need to update.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </signal>\n"
"  </interface>\n"
        "")
public:
    ApplicationUpdateNotifier1Adaptor(QObject *parent);
    virtual ~ApplicationUpdateNotifier1Adaptor();

public: // PROPERTIES
public Q_SLOTS: // METHODS
Q_SIGNALS: // SIGNALS
    void ApplicationUpdated();
};

#endif
