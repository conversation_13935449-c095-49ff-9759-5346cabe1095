/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-application-manager/api/dbus/org.desktopspec.JobManager1.Job.xml -a ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.JobManager1.JobAdaptor -i ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.JobManager1.Job.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.JobManager1.JobAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class JobAdaptor
 */

JobAdaptor::JobAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

JobAdaptor::~JobAdaptor()
{
    // destructor
}

QString JobAdaptor::status() const
{
    // get the value of property Status
    return qvariant_cast< QString >(parent()->property("Status"));
}

void JobAdaptor::Cancel()
{
    // handle method call org.desktopspec.JobManager1.Job.Cancel
    QMetaObject::invokeMethod(parent(), "Cancel");
}

void JobAdaptor::Resume()
{
    // handle method call org.desktopspec.JobManager1.Job.Resume
    QMetaObject::invokeMethod(parent(), "Resume");
}

void JobAdaptor::Suspend()
{
    // handle method call org.desktopspec.JobManager1.Job.Suspend
    QMetaObject::invokeMethod(parent(), "Suspend");
}

