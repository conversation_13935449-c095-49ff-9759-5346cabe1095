/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-application-manager/api/dbus/org.desktopspec.ApplicationManager1.Instance.xml -a ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.InstanceAdaptor -i ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.Instance.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.InstanceAdaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class InstanceAdaptor
 */

InstanceAdaptor::InstanceAdaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

InstanceAdaptor::~InstanceAdaptor()
{
    // destructor
}

QDBusObjectPath InstanceAdaptor::application() const
{
    // get the value of property Application
    return qvariant_cast< QDBusObjectPath >(parent()->property("Application"));
}

QString InstanceAdaptor::launcher() const
{
    // get the value of property Launcher
    return qvariant_cast< QString >(parent()->property("Launcher"));
}

bool InstanceAdaptor::orphaned() const
{
    // get the value of property Orphaned
    return qvariant_cast< bool >(parent()->property("Orphaned"));
}

QDBusObjectPath InstanceAdaptor::systemdUnitPath() const
{
    // get the value of property SystemdUnitPath
    return qvariant_cast< QDBusObjectPath >(parent()->property("SystemdUnitPath"));
}

void InstanceAdaptor::KillAll(int signal)
{
    // handle method call org.desktopspec.ApplicationManager1.Instance.KillAll
    QMetaObject::invokeMethod(parent(), "KillAll", Q_ARG(int, signal));
}

