/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-application-manager/api/dbus/org.desktopspec.JobManager1.Job.xml -a ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.JobManager1.JobAdaptor -i ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.JobManager1.Job.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DESKTOPSPEC_JOBMANAGER1_JOBADAPTOR_H
#define ORG_DESKTOPSPEC_JOBMANAGER1_JOBADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.JobManager1.Job.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.desktopspec.JobManager1.Job
 */
class JobAdaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.desktopspec.JobManager1.Job")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.desktopspec.JobManager1.Job\">\n"
"    <property access=\"read\" type=\"s\" name=\"Status\">\n"
"      <annotation value=\"Value of `Status` is one of                        `started`, `running`, `finished`,                        `suspending`, `suspend`,                        `canceled`.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </property>\n"
"    <method name=\"Cancel\">\n"
"      <annotation value=\"Success call to this method will                        change the Status of this Job to `canceled`.                        Then after some time this method call return,                        this Job will be removed.                        Then the signal JobRemoved of org.desktopspec.JobManager1                        will be emitted.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </method>\n"
"    <method name=\"Suspend\">\n"
"      <annotation value=\"Success call to this method will                        change the Status of this Job to `suspending`.                        Then after some time this method call return,                        Status will change to `suspend`.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </method>\n"
"    <method name=\"Resume\">\n"
"      <annotation value=\"Success call to this method will                        Resumes the asynchronous computation.                        Then after some time this method call return,                        Status will change to `working`.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </method>\n"
"  </interface>\n"
        "")
public:
    JobAdaptor(QObject *parent);
    virtual ~JobAdaptor();

public: // PROPERTIES
    Q_PROPERTY(QString Status READ status)
    QString status() const;

public Q_SLOTS: // METHODS
    void Cancel();
    void Resume();
    void Suspend();
Q_SIGNALS: // SIGNALS
};

#endif
