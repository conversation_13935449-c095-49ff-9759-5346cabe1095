/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-application-manager/api/dbus/org.desktopspec.ApplicationManager1.xml -a ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1Adaptor -i ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * Do not edit! All changes made to it will be lost.
 */

#include "./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.ApplicationManager1Adaptor.h"
#include <QtCore/QMetaObject>
#include <QtCore/QByteArray>
#include <QtCore/QList>
#include <QtCore/QMap>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QVariant>

/*
 * Implementation of adaptor class ApplicationManager1Adaptor
 */

ApplicationManager1Adaptor::ApplicationManager1Adaptor(QObject *parent)
    : QDBusAbstractAdaptor(parent)
{
    // constructor
    setAutoRelaySignals(true);
}

ApplicationManager1Adaptor::~ApplicationManager1Adaptor()
{
    // destructor
}

QList<QDBusObjectPath> ApplicationManager1Adaptor::list() const
{
    // get the value of property List
    return qvariant_cast< QList<QDBusObjectPath> >(parent()->property("List"));
}

QString ApplicationManager1Adaptor::Identify(const QDBusUnixFileDescriptor &pidfd, QDBusObjectPath &instance, ObjectInterfaceMap &application_instance_info)
{
    // handle method call org.desktopspec.ApplicationManager1.Identify
    //return static_cast<YourObjectType *>(parent())->Identify(pidfd, instance, application_instance_info);
}

void ApplicationManager1Adaptor::ReloadApplications()
{
    // handle method call org.desktopspec.ApplicationManager1.ReloadApplications
    QMetaObject::invokeMethod(parent(), "ReloadApplications");
}

QString ApplicationManager1Adaptor::addUserApplication(const QVariantMap &desktop_file, const QString &name)
{
    // handle method call org.desktopspec.ApplicationManager1.addUserApplication
    QString app_id;
    QMetaObject::invokeMethod(parent(), "addUserApplication", Q_RETURN_ARG(QString, app_id), Q_ARG(QVariantMap, desktop_file), Q_ARG(QString, name));
    return app_id;
}

void ApplicationManager1Adaptor::deleteUserApplication(const QString &app_id)
{
    // handle method call org.desktopspec.ApplicationManager1.deleteUserApplication
    QMetaObject::invokeMethod(parent(), "deleteUserApplication", Q_ARG(QString, app_id));
}

