/*
 * This file was generated by qdbusxml2cpp version 0.8
 * Command line was: qdbusxml2cpp ./dde-application-manager/api/dbus/org.desktopspec.JobManager1.xml -a ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.JobManager1Adaptor -i ./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.JobManager1.h
 *
 * qdbusxml2cpp is Copyright (C) 2017 The Qt Company Ltd.
 *
 * This is an auto-generated file.
 * This file may have been hand-edited. Look for HAND-EDIT comments
 * before re-generating it.
 */

#ifndef ORG_DESKTOPSPEC_JOBMANAGER1ADAPTOR_H
#define ORG_DESKTOPSPEC_JOBMANAGER1ADAPTOR_H

#include <QtCore/QObject>
#include <QtDBus/QtDBus>
#include "./dde-application-manager/toolGenerate/qdbusxml2cpp/org.desktopspec.JobManager1.h"
QT_BEGIN_NAMESPACE
class QByteArray;
template<class T> class QList;
template<class Key, class Value> class QMap;
class QString;
class QStringList;
class QVariant;
QT_END_NAMESPACE

/*
 * Adaptor class for interface org.desktopspec.JobManager1
 */
class JobManager1Adaptor: public QDBusAbstractAdaptor
{
    Q_OBJECT
    Q_CLASSINFO("D-Bus Interface", "org.desktopspec.JobManager1")
    Q_CLASSINFO("D-Bus Introspection", ""
"  <interface name=\"org.desktopspec.JobManager1\">\n"
"    <annotation value=\"All method might block will return a DBus object path,                    which implement org.desktopspec.JobManager1.Job.                    This interface is design to provide some DBus signals to help                    caller to watch the Jobs they invoked.                    Caller should not interst in all the Jobs, as there are some                    Jobs not created by them.                    So the method to list all exsiting Jobs is NOT provided.                    NOTE:                    Signal emitted by this interface MIGHT be peer-to-peer.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    <signal name=\"JobNew\">\n"
"      <arg type=\"o\" name=\"job\"/>\n"
"      <arg type=\"o\" name=\"source\"/>\n"
"      <annotation value=\"`source` is the DBus object produce this job.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </signal>\n"
"    <signal name=\"JobRemoved\">\n"
"      <arg type=\"o\" name=\"job\"/>\n"
"      <arg type=\"s\" name=\"status\"/>\n"
"      <arg type=\"av\" name=\"result\"/>\n"
"      <annotation value=\"This signal report the job remove event.                        `status` is the `status` porperty of that Job                        when it got removed.                        `result` is the result which produce by this job,                        and user needs to traverse the list to determine                         if the values are valid or not.                        Method that return an object of Job                        should always have document                        about the type and meaning of the `result`.\" name=\"org.freedesktop.DBus.Description\"/>\n"
"    </signal>\n"
"  </interface>\n"
        "")
public:
    JobManager1Adaptor(QObject *parent);
    virtual ~JobManager1Adaptor();

public: // PROPERTIES
public Q_SLOTS: // METHODS
Q_SIGNALS: // SIGNALS
    void JobNew(const QDBusObjectPath &job, const QDBusObjectPath &source);
    void JobRemoved(const QDBusObjectPath &job, const QString &status, const QVariantList &result);
};

#endif
