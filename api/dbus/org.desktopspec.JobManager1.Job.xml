<!DOCTYPE node PUBLIC "-//freedesktop//DTD D-BUS Object Introspection 1.0//EN" "https://www.freedesktop.org/standards/dbus/1.0/introspect.dtd">
<node>
    <interface name="org.desktopspec.JobManager1.Job">
        <property name="Status" type="s" access="read">
            <annotation
                name="org.freedesktop.DBus.Description"
                value="Value of `Status` is one of
                       `started`, `running`, `finished`,
                       `suspending`, `suspend`,
                       `canceled`."
            />
        </property>

        <method name="Cancel">
            <annotation
                name="org.freedesktop.DBus.Description"
                value="Success call to this method will
                       change the Status of this Job to `canceled`.
                       Then after some time this method call return,
                       this Job will be removed.
                       Then the signal JobRemoved of org.desktopspec.JobManager1
                       will be emitted."
            />
        </method>
        <method name="Suspend">
            <annotation
                name="org.freedesktop.DBus.Description"
                value="Success call to this method will
                       change the Status of this Job to `suspending`.
                       Then after some time this method call return,
                       Status will change to `suspend`."
            />
        </method>

        <method name="Resume">
            <annotation
                name="org.freedesktop.DBus.Description"
                value="Success call to this method will
                       Resumes the asynchronous computation.
                       Then after some time this method call return,
                       Status will change to `working`."
            />
        </method>
    </interface>
</node>
