Source: dde-application-manager
Section: utils
Priority: optional
Maintainer: <PERSON> <<EMAIL>>
Build-Depends: debhelper-compat ( =12), cmake,
 libgtest-dev,
 libsystemd-dev,
 pkg-config,
 qt6-base-dev,
 libdtkcommon-dev,
 libdtk6core-dev,
Standards-Version: 4.5.0
Homepage: https://github.com/linuxdeepin/dde-application-manager

Package: dde-application-manager
Architecture: any
Depends: ${misc:Depends}, ${shlibs:Depends}
Description:  Application manager for DDE.
  Launch applications by systemd for more caps.

Package: dde-application-manager-api
Architecture: any
Depends: dde-application-manager( =${binary:Version}), ${misc:Depends}
Description: DBus Interface of dde-application-manager.
  For the convenience of developers who want to use dde-application-manager.
